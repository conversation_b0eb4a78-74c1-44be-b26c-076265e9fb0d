# Use Node.js 18 Alpine as base image for smaller size
FROM node:18-alpine

# Install Python and ML dependencies
RUN apk add --no-cache \
  python3 \
  py3-pip \
  py3-numpy \
  py3-pandas \
  py3-scikit-learn \
  py3-scipy \
  build-base \
  python3-dev \
  curl \
  && ln -sf python3 /usr/bin/python

# Set working directory
WORKDIR /app

# Copy package files first for better Docker layer caching
COPY package*.json ./
COPY requirements.txt ./

# Install Node.js dependencies
RUN npm install --only=production && npm cache clean --force

# Create virtual environment and install Python dependencies
RUN python3 -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ ./src/
COPY models/ ./models/

# Create logs directory
RUN mkdir -p logs

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
  adduser -S aiservice -u 1001 -G nodejs

# Change ownership of app directory
RUN chown -R aiservice:nodejs /app

# Switch to non-root user
USER aiservice

# Expose port
EXPOSE 5002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5002/health || exit 1

# Ensure virtual environment is active and start the application
ENV PATH="/opt/venv/bin:$PATH"
CMD ["node", "src/index.js"]

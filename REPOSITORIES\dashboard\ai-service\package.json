{"name": "exlog-ai-service", "version": "1.0.0", "description": "AI-powered security insights service for ExLog", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "prepare-models": "node scripts/prepare-models.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "compression": "^1.7.4", "mongoose": "^7.5.0", "winston": "^3.10.0", "node-cron": "^3.0.2", "child_process": "^1.0.2", "fs-extra": "^11.1.1", "lodash": "^4.17.21", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.47.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["ai", "security", "anomaly-detection", "cybersecurity", "machine-learning"], "author": "ExLog Team", "license": "MIT"}
const mongoose = require('mongoose');

const aiConfigSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  version: {
    type: String,
    required: true,
    default: '1.0.0'
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  analysisSettings: {
    interval: {
      type: Number,
      required: true,
      default: 300, // 5 minutes in seconds
      min: 60,      // minimum 1 minute
      max: 86400    // maximum 24 hours
    },
    timeRange: {
      type: String,
      enum: ['15m', '30m', '1h', '2h', '6h', '12h', '24h'],
      default: '1h'
    },
    enabledLogTypes: [{
      type: String,
      enum: ['debug', 'info', 'warn', 'error', 'fatal'],
      default: ['warn', 'error', 'fatal']
    }],
    maxLogsPerAnalysis: {
      type: Number,
      default: 10000,
      min: 100,
      max: 100000
    },
    autoScheduling: {
      enabled: {
        type: Boolean,
        default: true
      },
      cronExpression: {
        type: String,
        default: null // Will be auto-generated from interval
      },
      timezone: {
        type: String,
        default: 'UTC'
      },
      maxConcurrentRuns: {
        type: Number,
        default: 1,
        min: 1,
        max: 5
      },
      skipIfRunning: {
        type: Boolean,
        default: true
      }
    },
    analysisTypes: {
      periodic: {
        type: Boolean,
        default: true
      },
      triggered: {
        type: Boolean,
        default: true
      },
      manual: {
        type: Boolean,
        default: true
      }
    }
  },
  anomalyDetection: {
    enabled: {
      type: Boolean,
      default: true
    },
    threshold: {
      type: Number,
      default: 0.1,
      min: 0.01,
      max: 0.5
    },
    sensitivity: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    minAnomaliesForAlert: {
      type: Number,
      default: 5,
      min: 1,
      max: 100
    }
  },
  threatPrediction: {
    enabled: {
      type: Boolean,
      default: true
    },
    confidenceThreshold: {
      type: Number,
      default: 70,
      min: 50,
      max: 95
    },
    enabledPatterns: [{
      type: String,
      default: ['bruteForce', 'dataExfiltration', 'privilegeEscalation', 'malwareActivity', 'networkIntrusion']
    }],
    alertOnCritical: {
      type: Boolean,
      default: true
    }
  },
  patternMatching: {
    enabled: {
      type: Boolean,
      default: true
    },
    enableMitreAttack: {
      type: Boolean,
      default: true
    },
    enableCustomRules: {
      type: Boolean,
      default: true
    },
    customRules: [{
      id: String,
      name: String,
      description: String,
      pattern: String,
      severity: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical']
      },
      enabled: {
        type: Boolean,
        default: true
      }
    }]
  },
  alerting: {
    enabled: {
      type: Boolean,
      default: true
    },
    riskLevelThreshold: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium'
    },
    emailNotifications: {
      enabled: {
        type: Boolean,
        default: false
      },
      recipients: [String],
      template: String
    },
    webhookNotifications: {
      enabled: {
        type: Boolean,
        default: false
      },
      url: String,
      headers: {
        type: Map,
        of: String
      }
    },
    maxAlertsPerHour: {
      type: Number,
      default: 10,
      min: 1,
      max: 100
    }
  },
  performance: {
    maxMemoryUsage: {
      type: Number,
      default: 2048, // MB
      min: 512,
      max: 8192
    },
    maxCpuUsage: {
      type: Number,
      default: 80, // percentage
      min: 50,
      max: 95
    },
    cacheSize: {
      type: Number,
      default: 1000, // number of cached results
      min: 100,
      max: 10000
    },
    cacheTtl: {
      type: Number,
      default: 300, // seconds
      min: 60,
      max: 3600
    }
  },
  dataRetention: {
    insightsRetentionDays: {
      type: Number,
      default: 90,
      min: 7,
      max: 365
    },
    feedbackRetentionDays: {
      type: Number,
      default: 365,
      min: 30,
      max: 1095
    },
    performanceMetricsRetentionDays: {
      type: Number,
      default: 30,
      min: 7,
      max: 90
    },
    analysisResultsRetentionDays: {
      type: Number,
      default: 180,
      min: 30,
      max: 730
    },
    autoCleanup: {
      enabled: {
        type: Boolean,
        default: true
      },
      schedule: {
        type: String,
        default: '0 2 * * *' // Daily at 2 AM
      },
      batchSize: {
        type: Number,
        default: 1000,
        min: 100,
        max: 10000
      }
    },
    compressionSettings: {
      enabled: {
        type: Boolean,
        default: true
      },
      compressAfterDays: {
        type: Number,
        default: 30,
        min: 7,
        max: 90
      }
    }
  },
  modelSettings: {
    isolationForestContamination: {
      type: Number,
      default: 0.1,
      min: 0.01,
      max: 0.3
    },
    isolationForestEstimators: {
      type: Number,
      default: 100,
      min: 50,
      max: 500
    },
    tfidfMaxFeatures: {
      type: Number,
      default: 1000,
      min: 100,
      max: 5000
    },
    autoRetraining: {
      enabled: {
        type: Boolean,
        default: false
      },
      intervalDays: {
        type: Number,
        default: 30,
        min: 7,
        max: 90
      },
      minFeedbackSamples: {
        type: Number,
        default: 100,
        min: 50,
        max: 1000
      }
    }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  collection: 'ai_config'
});

// Indexes for better query performance
aiConfigSchema.index({ name: 1 });
aiConfigSchema.index({ isActive: 1 });
aiConfigSchema.index({ createdAt: -1 });
aiConfigSchema.index({ version: 1 });

// Virtual for formatted creation date
aiConfigSchema.virtual('formattedCreatedAt').get(function () {
  return this.createdAt.toISOString();
});

// Method to validate configuration
aiConfigSchema.methods.validateConfig = function () {
  const errors = [];

  // Validate analysis settings
  if (this.analysisSettings.interval < 60 || this.analysisSettings.interval > 86400) {
    errors.push('Analysis interval must be between 60 and 86400 seconds');
  }

  // Validate anomaly detection settings
  if (this.anomalyDetection.threshold < 0.01 || this.anomalyDetection.threshold > 0.5) {
    errors.push('Anomaly detection threshold must be between 0.01 and 0.5');
  }

  // Validate threat prediction settings
  if (this.threatPrediction.confidenceThreshold < 50 || this.threatPrediction.confidenceThreshold > 95) {
    errors.push('Threat prediction confidence threshold must be between 50 and 95');
  }

  // Validate performance settings
  if (this.performance.maxMemoryUsage < 512 || this.performance.maxMemoryUsage > 8192) {
    errors.push('Max memory usage must be between 512 and 8192 MB');
  }

  // Validate scheduling settings
  if (this.analysisSettings.autoScheduling.maxConcurrentRuns < 1 || this.analysisSettings.autoScheduling.maxConcurrentRuns > 5) {
    errors.push('Max concurrent runs must be between 1 and 5');
  }

  // Validate retention settings
  if (this.dataRetention.analysisResultsRetentionDays < 30 || this.dataRetention.analysisResultsRetentionDays > 730) {
    errors.push('Analysis results retention must be between 30 and 730 days');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Method to generate cron expression from interval
aiConfigSchema.methods.generateCronExpression = function () {
  const intervalSeconds = this.analysisSettings.interval;

  if (intervalSeconds < 60) {
    throw new Error('Interval must be at least 60 seconds');
  }

  if (intervalSeconds < 3600) {
    // Less than an hour - use minute intervals
    const minutes = Math.floor(intervalSeconds / 60);
    return `*/${minutes} * * * *`;
  } else if (intervalSeconds < 86400) {
    // Less than a day - use hour intervals
    const hours = Math.floor(intervalSeconds / 3600);
    return `0 */${hours} * * *`;
  } else {
    // Daily or more
    const days = Math.floor(intervalSeconds / 86400);
    if (days === 1) {
      return '0 0 * * *'; // Daily at midnight
    } else {
      return `0 0 */${days} * *`; // Every N days
    }
  }
};

// Method to get scheduling configuration
aiConfigSchema.methods.getSchedulingConfig = function () {
  return {
    enabled: this.analysisSettings.autoScheduling.enabled,
    cronExpression: this.analysisSettings.autoScheduling.cronExpression || this.generateCronExpression(),
    timezone: this.analysisSettings.autoScheduling.timezone,
    maxConcurrentRuns: this.analysisSettings.autoScheduling.maxConcurrentRuns,
    skipIfRunning: this.analysisSettings.autoScheduling.skipIfRunning,
    interval: this.analysisSettings.interval,
    timeRange: this.analysisSettings.timeRange
  };
};

// Method to get configuration summary
aiConfigSchema.methods.getSummary = function () {
  return {
    name: this.name,
    version: this.version,
    isActive: this.isActive,
    analysisInterval: this.analysisSettings.interval,
    anomalyDetectionEnabled: this.anomalyDetection.enabled,
    threatPredictionEnabled: this.threatPrediction.enabled,
    patternMatchingEnabled: this.patternMatching.enabled,
    alertingEnabled: this.alerting.enabled,
    lastModified: this.updatedAt
  };
};

// Static method to get active configuration
aiConfigSchema.statics.getActiveConfig = function () {
  return this.findOne({ isActive: true });
};

// Static method to create default configuration
aiConfigSchema.statics.createDefaultConfig = async function (userId) {
  const defaultConfig = new this({
    name: 'Default AI Configuration',
    description: 'Default configuration for AI insights service',
    version: '1.0.0',
    isActive: true,
    createdBy: userId
  });

  return await defaultConfig.save();
};

// Static method to activate configuration
aiConfigSchema.statics.activateConfig = async function (configId) {
  // Deactivate all configurations
  await this.updateMany({}, { isActive: false });

  // Activate the specified configuration
  return await this.findByIdAndUpdate(
    configId,
    { isActive: true },
    { new: true }
  );
};

// Pre-save middleware to ensure only one active configuration
aiConfigSchema.pre('save', async function (next) {
  if (this.isActive && this.isModified('isActive')) {
    // Deactivate all other configurations
    await this.constructor.updateMany(
      { _id: { $ne: this._id } },
      { isActive: false }
    );
  }
  next();
});

// Method to clone configuration
aiConfigSchema.methods.clone = function (newName, userId) {
  const clonedConfig = new this.constructor({
    ...this.toObject(),
    _id: undefined,
    name: newName,
    isActive: false,
    createdBy: userId,
    createdAt: undefined,
    updatedAt: undefined
  });

  return clonedConfig.save();
};

module.exports = mongoose.model('AIConfig', aiConfigSchema);

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  Box,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Tooltip,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Paper
} from '@mui/material';
import {
  Visibility,
  Refresh,
  FilterList,
  Download,
  TrendingUp,
  Security,
  BugReport,
  Schedule
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { formatDistanceToNow, format } from 'date-fns';
import { aiService } from '../../services/api';

const AIAnalysisHistory = () => {
  const theme = useTheme();
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedResult, setSelectedResult] = useState(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [filters, setFilters] = useState({
    startTime: '',
    endTime: '',
    riskLevel: '',
    type: ''
  });

  useEffect(() => {
    loadResults();
  }, [page, rowsPerPage, filters]);

  const loadResults = async () => {
    try {
      setLoading(true);
      const params = {
        limit: rowsPerPage,
        offset: page * rowsPerPage,
        ...filters
      };
      
      const response = await aiService.getAnalysisResults(params);
      setResults(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to load analysis results');
      console.error('Error loading analysis results:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = async (analysisId) => {
    try {
      const response = await aiService.getAnalysisResult(analysisId);
      setSelectedResult(response.data);
      setDetailsOpen(true);
    } catch (err) {
      console.error('Error loading analysis details:', err);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPage(0); // Reset to first page when filtering
  };

  const getRiskLevelColor = (level) => {
    switch (level) {
      case 'low': return 'success';
      case 'medium': return 'warning';
      case 'high': return 'error';
      case 'critical': return 'error';
      default: return 'default';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'running': return 'info';
      case 'failed': return 'error';
      case 'cancelled': return 'default';
      default: return 'default';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'periodic': return <Schedule />;
      case 'manual': return <Visibility />;
      case 'triggered': return <TrendingUp />;
      default: return <Schedule />;
    }
  };

  if (loading && results.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Card>
        <CardHeader
          title="AI Analysis History"
          subheader="View past analysis results and performance metrics"
          action={
            <Button
              startIcon={<Refresh />}
              onClick={loadResults}
              disabled={loading}
            >
              Refresh
            </Button>
          }
        />
        <CardContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {/* Filters */}
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              <FilterList sx={{ mr: 1, verticalAlign: 'middle' }} />
              Filters
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Start Date"
                  type="datetime-local"
                  value={filters.startTime}
                  onChange={(e) => handleFilterChange('startTime', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="End Date"
                  type="datetime-local"
                  value={filters.endTime}
                  onChange={(e) => handleFilterChange('endTime', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Risk Level</InputLabel>
                  <Select
                    value={filters.riskLevel}
                    onChange={(e) => handleFilterChange('riskLevel', e.target.value)}
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="low">Low</MenuItem>
                    <MenuItem value="medium">Medium</MenuItem>
                    <MenuItem value="high">High</MenuItem>
                    <MenuItem value="critical">Critical</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Analysis Type</InputLabel>
                  <Select
                    value={filters.type}
                    onChange={(e) => handleFilterChange('type', e.target.value)}
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="periodic">Periodic</MenuItem>
                    <MenuItem value="manual">Manual</MenuItem>
                    <MenuItem value="triggered">Triggered</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Paper>

          {/* Results Table */}
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Analysis ID</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Started</TableCell>
                  <TableCell>Duration</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Risk Level</TableCell>
                  <TableCell>Findings</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {results.map((result) => (
                  <TableRow key={result.analysisId}>
                    <TableCell>
                      <Typography variant="body2" fontFamily="monospace">
                        {result.analysisId.substring(0, 8)}...
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        {getTypeIcon(result.type)}
                        <Typography variant="body2" sx={{ ml: 1 }}>
                          {result.type}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Tooltip title={format(new Date(result.execution.startedAt), 'PPpp')}>
                        <Typography variant="body2">
                          {formatDistanceToNow(new Date(result.execution.startedAt), { addSuffix: true })}
                        </Typography>
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {result.execution.duration 
                          ? `${Math.round(result.execution.duration / 1000)}s`
                          : '-'
                        }
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={result.status}
                        color={getStatusColor(result.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={result.summary.riskLevel}
                        color={getRiskLevelColor(result.summary.riskLevel)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        {result.summary.anomalies > 0 && (
                          <Chip
                            icon={<BugReport />}
                            label={result.summary.anomalies}
                            size="small"
                            variant="outlined"
                          />
                        )}
                        {result.summary.threats > 0 && (
                          <Chip
                            icon={<Security />}
                            label={result.summary.threats}
                            size="small"
                            variant="outlined"
                            color="error"
                          />
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => handleViewDetails(result.analysisId)}
                        >
                          <Visibility />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            component="div"
            count={-1} // Unknown total count
            page={page}
            onPageChange={(e, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
            rowsPerPageOptions={[5, 10, 25, 50]}
          />
        </CardContent>
      </Card>

      {/* Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Analysis Details
          {selectedResult && (
            <Typography variant="subtitle2" color="textSecondary">
              {selectedResult.analysisId}
            </Typography>
          )}
        </DialogTitle>
        <DialogContent>
          {selectedResult && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>Summary</Typography>
                <Box>
                  <Typography variant="body2">
                    <strong>Total Logs:</strong> {selectedResult.summary.totalLogs}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Anomalies:</strong> {selectedResult.summary.anomalies}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Threats:</strong> {selectedResult.summary.threats}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Patterns:</strong> {selectedResult.summary.patterns}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Confidence:</strong> {selectedResult.summary.confidence}%
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>Performance</Typography>
                <Box>
                  <Typography variant="body2">
                    <strong>Duration:</strong> {Math.round(selectedResult.execution.duration / 1000)}s
                  </Typography>
                  <Typography variant="body2">
                    <strong>Processed Logs:</strong> {selectedResult.execution.processedLogs}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Processing Rate:</strong> {selectedResult.logsPerSecond} logs/sec
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AIAnalysisHistory;

import React, { useState, useEffect } from 'react'
import {
  Box,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Alert,
  CircularProgress,
  OutlinedInput,
} from '@mui/material'
import {
  Search,
  Download,
  Save,
  Visibility,
  Delete,
  GetApp,
  PictureAsPdf,
  TableChart,
  DateRange,
} from '@mui/icons-material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { format, subDays } from 'date-fns'
import api from '../../services/api'
import { CSVLink } from 'react-csv'
import jsPDF from 'jspdf'
import 'jspdf-autotable'

const SimpleReports = () => {
  // Form state
  const [startDate, setStartDate] = useState(subDays(new Date(), 7))
  const [endDate, setEndDate] = useState(new Date())
  const [logCategories, setLogCategories] = useState([])
  const [logLevels, setLogLevels] = useState([])
  const [agentTypes, setAgentTypes] = useState([])
  const [searchKeywords, setSearchKeywords] = useState('')

  // Results state
  const [reportData, setReportData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Saved reports state
  const [savedReports, setSavedReports] = useState([])
  const [loadingSaved, setLoadingSaved] = useState(false)

  // Pagination
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(25)

  // Dialogs
  const [saveDialogOpen, setSaveDialogOpen] = useState(false)
  const [reportName, setReportName] = useState('')
  const [reportDescription, setReportDescription] = useState('')

  // Available options
  const categoryOptions = ['System', 'Application', 'Security', 'Network', 'Custom']
  const levelOptions = ['critical', 'error', 'warning', 'info', 'debug']
  const agentTypeOptions = ['Windows', 'Linux']

  useEffect(() => {
    fetchSavedReports()
  }, [])

  const fetchSavedReports = async () => {
    setLoadingSaved(true)
    try {
      const response = await api.get('/simple-reports/list')
      setSavedReports(response.data.data.reports)
    } catch (err) {
      console.error('Failed to fetch saved reports:', err)
    } finally {
      setLoadingSaved(false)
    }
  }

  const generateReport = async () => {
    setLoading(true)
    setError(null)

    try {
      // Build query parameters for the logs API
      const params = new URLSearchParams()

      // Date range
      if (startDate) {
        params.append('startTime', startDate.toISOString())
      }
      if (endDate) {
        params.append('endTime', endDate.toISOString())
      }

      // Filters - only add if single values are selected
      if (logLevels.length === 1) {
        params.append('logLevel', logLevels[0])
      }
      if (logCategories.length === 1) {
        params.append('source', logCategories[0])
      }

      // Search
      if (searchKeywords) {
        params.append('search', searchKeywords)
      }

      // Pagination
      params.append('limit', '1000')
      params.append('page', '1')

      const response = await api.get(`/logs?${params.toString()}`)

      // Transform the response to match our expected format
      const transformedData = {
        logs: response.data.data.logs,
        summary: {
          totalLogs: response.data.data.pagination.totalCount,
          dateRange: {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString()
          }
        }
      }

      setReportData(transformedData)
      setPage(0) // Reset pagination
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to generate report')
    } finally {
      setLoading(false)
    }
  }

  const saveReport = async () => {
    if (!reportData || !reportName.trim()) return

    try {
      await api.post('/simple-reports/save', {
        name: reportName,
        description: reportDescription,
        filters: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          logCategories,
          logLevels,
          agentTypes,
          searchKeywords
        },
        resultSnapshot: {
          totalRecords: reportData.summary?.totalLogs || reportData.logs.length,
          data: reportData.logs
        }
      })

      setSaveDialogOpen(false)
      setReportName('')
      setReportDescription('')
      fetchSavedReports()
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save report')
    }
  }

  const deleteSavedReport = async (reportId) => {
    try {
      await api.delete(`/simple-reports/${reportId}`)
      fetchSavedReports()
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to delete report')
    }
  }

  const viewSavedReport = async (reportId) => {
    try {
      const response = await api.get(`/simple-reports/${reportId}`)
      const report = response.data.data.report

      // Load the saved report data
      setReportData({
        logs: report.resultSnapshot.data,
        summary: {
          totalLogs: report.resultSnapshot.totalRecords
        }
      })

      // Update form with saved filters
      setStartDate(new Date(report.filters.startDate))
      setEndDate(new Date(report.filters.endDate))
      setLogCategories(report.filters.logCategories || [])
      setLogLevels(report.filters.logLevels || [])
      setAgentTypes(report.filters.agentTypes || [])
      setSearchKeywords(report.filters.searchKeywords || '')

    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load report')
    }
  }

  const downloadPDF = () => {
    if (!reportData || !reportData.logs) return

    const doc = new jsPDF()

    // Add title
    doc.setFontSize(20)
    doc.text('ExLog Security Report', 20, 20)

    // Add metadata
    doc.setFontSize(12)
    doc.text(`Generated: ${format(new Date(), 'PPpp')}`, 20, 35)
    doc.text(`Date Range: ${format(startDate, 'PP')} - ${format(endDate, 'PP')}`, 20, 45)
    doc.text(`Total Records: ${reportData.summary?.totalLogs || reportData.logs.length}`, 20, 55)
    doc.text(`Displayed Records: ${Math.min(rowsPerPage, reportData.logs.length - (page * rowsPerPage))}`, 20, 65)

    // Prepare table data - use correct field names from our log structure
    const tableData = reportData.logs.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map(log => [
      format(new Date(log.timestamp), 'MM/dd/yyyy HH:mm'),
      log.host || 'N/A',
      log.logLevel || 'N/A',
      log.category || log.source || 'N/A',
      (log.message || '').substring(0, 50) + ((log.message || '').length > 50 ? '...' : ''),
      log.source || 'N/A',
      log.metadata?.agentId || 'N/A'
    ])

    // Add table
    doc.autoTable({
      head: [['Timestamp', 'Host', 'Level', 'Category', 'Message', 'Source', 'Agent ID']],
      body: tableData,
      startY: 75,
      styles: { fontSize: 8 },
      columnStyles: {
        0: { cellWidth: 25 },
        1: { cellWidth: 20 },
        2: { cellWidth: 15 },
        3: { cellWidth: 20 },
        4: { cellWidth: 40 },
        5: { cellWidth: 20 },
        6: { cellWidth: 15 }
      }
    })

    doc.save(`ExLog_Report_${format(new Date(), 'yyyy-MM-dd_HH-mm')}.pdf`)
  }

  const csvData = reportData ? reportData.logs.map(log => ({
    Timestamp: format(new Date(log.timestamp), 'yyyy-MM-dd HH:mm:ss'),
    Host: log.host || 'N/A',
    'Log Level': log.logLevel || 'N/A',
    Category: log.category || log.source || 'N/A',
    Message: log.message || 'N/A',
    Source: log.source || 'N/A',
    'Source Type': log.sourceType || 'N/A',
    'Agent ID': log.metadata?.agentId || 'N/A',
    Severity: log.severity || 'N/A'
  })) : []

  const handleChangePage = (event, newPage) => {
    setPage(newPage)
  }

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Typography variant="h4" component="h1" gutterBottom>
          Reports
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Generate reports from log data with customizable filters and export options.
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Report Builder Form */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Report Builder
            </Typography>

            <Grid container spacing={3}>
              {/* Date Range */}
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Start Date"
                  value={startDate}
                  onChange={setStartDate}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="End Date"
                  value={endDate}
                  onChange={setEndDate}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>

              {/* Log Category */}
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Log Categories</InputLabel>
                  <Select
                    multiple
                    value={logCategories}
                    onChange={(e) => setLogCategories(e.target.value)}
                    input={<OutlinedInput label="Log Categories" />}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} size="small" />
                        ))}
                      </Box>
                    )}
                  >
                    {categoryOptions.map((category) => (
                      <MenuItem key={category} value={category}>
                        {category}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Log Level */}
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Log Levels</InputLabel>
                  <Select
                    multiple
                    value={logLevels}
                    onChange={(e) => setLogLevels(e.target.value)}
                    input={<OutlinedInput label="Log Levels" />}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} size="small" />
                        ))}
                      </Box>
                    )}
                  >
                    {levelOptions.map((level) => (
                      <MenuItem key={level} value={level}>
                        {level}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Agent Type */}
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Agent Types</InputLabel>
                  <Select
                    multiple
                    value={agentTypes}
                    onChange={(e) => setAgentTypes(e.target.value)}
                    input={<OutlinedInput label="Agent Types" />}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} size="small" />
                        ))}
                      </Box>
                    )}
                  >
                    {agentTypeOptions.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Search Keywords */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Search Keywords"
                  placeholder="Search in message or source..."
                  value={searchKeywords}
                  onChange={(e) => setSearchKeywords(e.target.value)}
                  InputProps={{
                    startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                />
              </Grid>

              {/* Generate Button */}
              <Grid item xs={12}>
                <Button
                  variant="contained"
                  size="large"
                  onClick={generateReport}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <Search />}
                >
                  {loading ? 'Generating...' : 'Generate Report'}
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Report Results */}
        {reportData && (
          <Card sx={{ mb: 4 }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Report Results ({reportData.returnedRecords} of {reportData.totalRecords} records)
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    startIcon={<PictureAsPdf />}
                    onClick={downloadPDF}
                  >
                    Download PDF
                  </Button>
                  <CSVLink
                    data={csvData}
                    filename={`ExLog_Report_${format(new Date(), 'yyyy-MM-dd_HH-mm')}.csv`}
                    style={{ textDecoration: 'none' }}
                  >
                    <Button
                      variant="outlined"
                      startIcon={<TableChart />}
                    >
                      Download CSV
                    </Button>
                  </CSVLink>
                  <Button
                    variant="contained"
                    startIcon={<Save />}
                    onClick={() => setSaveDialogOpen(true)}
                  >
                    Save Report
                  </Button>
                </Box>
              </Box>

              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Timestamp</TableCell>
                      <TableCell>Hostname</TableCell>
                      <TableCell>Log Level</TableCell>
                      <TableCell>Category</TableCell>
                      <TableCell>Message</TableCell>
                      <TableCell>Source</TableCell>
                      <TableCell>Agent Type</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {reportData.logs
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((log, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            {format(new Date(log.timestamp), 'MM/dd/yyyy HH:mm:ss')}
                          </TableCell>
                          <TableCell>{log.hostname}</TableCell>
                          <TableCell>
                            <Chip
                              label={log.logLevel}
                              size="small"
                              color={
                                log.logLevel === 'critical' ? 'error' :
                                  log.logLevel === 'error' ? 'error' :
                                    log.logLevel === 'warning' ? 'warning' :
                                      'default'
                              }
                            />
                          </TableCell>
                          <TableCell>{log.category}</TableCell>
                          <TableCell sx={{ maxWidth: 300, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                            {log.message}
                          </TableCell>
                          <TableCell>{log.source}</TableCell>
                          <TableCell>{log.agentType}</TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                rowsPerPageOptions={[10, 25, 50, 100]}
                component="div"
                count={reportData.logs.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            </CardContent>
          </Card>
        )}

        {/* Saved Reports */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Saved Reports
            </Typography>

            {loadingSaved ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : savedReports.length === 0 ? (
              <Typography color="text.secondary" sx={{ textAlign: 'center', p: 3 }}>
                No saved reports found. Generate and save a report to see it here.
              </Typography>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell>Date Range</TableCell>
                      <TableCell>Records</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {savedReports.map((report) => (
                      <TableRow key={report.reportId}>
                        <TableCell>{report.name}</TableCell>
                        <TableCell>{report.description || '-'}</TableCell>
                        <TableCell>
                          {format(new Date(report.filters.startDate), 'MM/dd/yyyy')} - {' '}
                          {format(new Date(report.filters.endDate), 'MM/dd/yyyy')}
                        </TableCell>
                        <TableCell>{report.resultSnapshot.totalRecords}</TableCell>
                        <TableCell>{format(new Date(report.createdAt), 'MM/dd/yyyy HH:mm')}</TableCell>
                        <TableCell>
                          <IconButton
                            size="small"
                            onClick={() => viewSavedReport(report.reportId)}
                            title="View Report"
                          >
                            <Visibility />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => deleteSavedReport(report.reportId)}
                            title="Delete Report"
                            color="error"
                          >
                            <Delete />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </CardContent>
        </Card>

        {/* Save Report Dialog */}
        <Dialog open={saveDialogOpen} onClose={() => setSaveDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Save Report</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="Report Name"
              fullWidth
              variant="outlined"
              value={reportName}
              onChange={(e) => setReportName(e.target.value)}
              sx={{ mb: 2 }}
            />
            <TextField
              margin="dense"
              label="Description (Optional)"
              fullWidth
              multiline
              rows={3}
              variant="outlined"
              value={reportDescription}
              onChange={(e) => setReportDescription(e.target.value)}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setSaveDialogOpen(false)}>Cancel</Button>
            <Button onClick={saveReport} variant="contained" disabled={!reportName.trim()}>
              Save
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  )
}

export default SimpleReports
